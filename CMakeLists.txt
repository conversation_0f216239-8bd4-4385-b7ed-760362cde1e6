﻿# CMakeList.txt: gerrit_client 的 CMake 项目，在此处包括源代码并定义
# 项目特定的逻辑。
#
cmake_minimum_required(VERSION 3.10)
set(CMAKE_WIN32_EXECUTABLE ON)

# 如果支持，请为 MSVC 编译器启用热重载。
if (POLICY CMP0141)
  cmake_policy(SET CMP0141 NEW)
  set(CMAKE_MSVC_DEBUG_INFORMATION_FORMAT "$<IF:$<AND:$<C_COMPILER_ID:MSVC>,$<CXX_COMPILER_ID:MSVC>>,$<$<CONFIG:Debug,RelWithDebInfo>:EditAndContinue>,$<$<CONFIG:Debug,RelWithDebInfo>:ProgramDatabase>>")
endif()

# 设置C++标准
set(CMAKE_CXX_STANDARD 20)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)  # 确保生成 compile_commands.json

# 设置资源编译器
if(MSVC)
    set(CMAKE_RC_COMPILER_INIT rc)
    enable_language(RC)
    set(CMAKE_RC_FLAGS "${CMAKE_RC_FLAGS} /utf-8")
endif()


# 强制使用 UTF-8 编码
add_compile_options("$<$<CXX_COMPILER_ID:MSVC>:/utf-8>")
# 启用 Unicode 支持
add_compile_definitions(UNICODE _UNICODE)

if(MSVC)
    add_definitions(-D_WIN32_WINNT=0x0A00)  # Windows 10
    add_definitions(-DWINVER=0x0A00)         # Windows 10
    add_definitions(-DNTDDI_VERSION=0x0A000000) # Windows 10
endif()

project("gerrit_client" VERSION 1.0.0 LANGUAGES C CXX RC)

# 添加子目录（子库）
# add_subdirectory(easy_direct_composition)

# 查找依赖包
find_package(CURL REQUIRED)
find_package(nlohmann_json CONFIG REQUIRED)

# 创建主可执行文件
add_executable(gerrit_client
    "gerrit_client.cpp"
    "gerrit_client.h"
    easy_direct_composition/src/obj_tree.c
    easy_direct_composition/src/obj_tree.h
    easy_direct_composition/src/obj_helper.cpp
    easy_direct_composition/src/obj_helper.h
    easy_direct_composition/src/dc_env.cpp
    easy_direct_composition/src/dc_env.h
    easy_direct_composition/src/dc_surface.cpp
    easy_direct_composition/src/dc_surface.h
    easy_direct_composition/src/dc_gui.cpp
    easy_direct_composition/src/dc_gui.h
    easy_direct_composition/src/resources.rc
    easy_direct_composition/src/resource.h
    api/gerrit_api.h
    api/gerrit_api.cpp
    api/ollama_api.h
    api/ollama_api.cpp
)

# 链接子库
target_link_libraries(gerrit_client
    PRIVATE
#        EasyDirectComposition::easy_direct_composition
        CURL::libcurl
        nlohmann_json::nlohmann_json
)
target_link_libraries(gerrit_client
    PUBLIC
        d3d11
        dcomp
        d2d1
        dwrite
        dxgi
        ole32
        uuid
        version
        comctl32
)


# 设置包含目录
target_include_directories(gerrit_client
    PRIVATE
        ${CMAKE_CURRENT_SOURCE_DIR}
)

if(WIN32)
  # 指定清单文件路径
  set(MANIFEST "${CMAKE_CURRENT_SOURCE_DIR}/app.manifest")
    
  # 嵌入清单文件 (MSVC)
  if(MSVC)
    target_sources(gerrit_client PRIVATE ${MANIFEST})
  
  # MinGW 使用资源文件嵌入
  elseif(MINGW)
    configure_file(${MANIFEST} ${CMAKE_CURRENT_BINARY_DIR}/app.manifest COPYONLY)
    file(WRITE ${CMAKE_CURRENT_BINARY_DIR}/manifest.rc "1 24 \"${CMAKE_CURRENT_BINARY_DIR}/app.manifest\"\n")
    target_sources(gerrit_client PRIVATE ${CMAKE_CURRENT_BINARY_DIR}/manifest.rc)
  endif()
endif()

# TODO: 如有需要，请添加测试并安装目标。
