﻿// gerrit_client.cpp: 定义应用程序的入口点。
//

#include "gerrit_client.h"
#include "easy_direct_composition/easy_direct_composition.h"
#include "easy_direct_composition/src/dc_gui.h"
#include <Windowsx.h>
#include <nlohmann/json.hpp>
#include "api/gerrit_api.h"
#include "api/ollama_api.h"
#include <curl/curl.h>

using namespace std;
//using json = nlohmann::json;

Easy_Object rect, text;
CComPtr<IDWriteTextFormat> text_format;
CComPtr<IDWriteTextFormat> text_format1;
edc::SurfaceHelper* global_surface_helper;
edc::Environment* global_dc_env;

DC_GUI *global_gui;

LRESULT mouse_move_handler(edc::Object data, HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam)
{
	//{
	//	std::lock_guard<std::mutex> lock(global_surface_helper->lock());
	//	int x_pos = GET_X_LPARAM(lParam);
	//	int y_pos = GET_Y_LPARAM(lParam);
	//	auto rect_data = global_surface_helper->getComponentData<edc::RectData>(rect);
	//	rect_data->x = (float)x_pos;
	//	rect_data->y = (float)y_pos;
	//	auto text_data = global_surface_helper->getComponentData<Text_Data>(text);
	//	text_data->x = (float)x_pos;
	//	text_data->y = (float)y_pos;
	//}
	//global_surface_helper->compile();
	//global_dc_env->commit();
    return 0;
}

static DC_GUI_Style s_style1,s_style2;

static void debug_output(const std::string &s)
{
	std::wstring ws;
	ws = DC_Env::utf8_to_wstring(s);
	OutputDebugStringW(ws.c_str());
}

// Provides the entry point to the application.
INT WINAPI wWinMain(_In_ HINSTANCE hInstance, _In_opt_ HINSTANCE, _In_ LPWSTR, _In_ INT)
{
	int ret = 0;
	{
		CURLcode res;
		res = curl_global_init(CURL_GLOBAL_DEFAULT);
		assert(res == CURLE_OK);
		// Initialize the Easy Direct Composition library
		Easy_Object_Seat root_object = edc::initialize();
		// Create the Direct Composition environment
		edc::Environment dc_inst(hInstance, root_object);
		assert(SUCCEEDED(dc_inst.Initialize()));
		global_dc_env = &dc_inst;


		// Create a visual and surface
		edc::Object visual1 = dc_inst.makeVisual(dc_inst.getRootVisual());
		edc::Object surface1 = dc_inst.createSwapChainSurfaceForVisual(visual1, {0, 0, 400, 400});

		// Create surface helper and add a rectangle
		//edc::SurfaceHelper surface_helper(surface1);
		//global_surface_helper = &surface_helper;
		//edc::RectData rect_data = edc::make_rect(100, 100, 100, 100, 0.6f, 1.0f, 1.0f, 0.9f);
		CComPtr<IDWriteFactory> dwriteFactory = dc_inst.getDwriteFactory();
		dwriteFactory->CreateTextFormat(
			L"Segoe UI",         // 字体
			nullptr,             // 字体集合(nullptr表示系统默认)
			DWRITE_FONT_WEIGHT_NORMAL,
			DWRITE_FONT_STYLE_NORMAL,
			DWRITE_FONT_STRETCH_NORMAL,
			12.0f,              // 字号
			L"en-us",           // 区域设置
			&text_format
		);
		text_format->SetTextAlignment(DWRITE_TEXT_ALIGNMENT_LEADING);
		text_format->SetParagraphAlignment(DWRITE_PARAGRAPH_ALIGNMENT_NEAR);
		dwriteFactory->CreateTextFormat(
			L"Segoe UI",         // 字体
			nullptr,             // 字体集合(nullptr表示系统默认)
			DWRITE_FONT_WEIGHT_NORMAL,
			DWRITE_FONT_STYLE_NORMAL,
			DWRITE_FONT_STRETCH_NORMAL,
			9.0f,              // 字号
			L"en-us",           // 区域设置
			&text_format1
		);
		text_format1->SetTextAlignment(DWRITE_TEXT_ALIGNMENT_LEADING);
		text_format1->SetParagraphAlignment(DWRITE_PARAGRAPH_ALIGNMENT_CENTER);
		text_format1->SetWordWrapping(DWRITE_WORD_WRAPPING_NO_WRAP);
		DWRITE_TRIMMING trimming{ DWRITE_TRIMMING_GRANULARITY_CHARACTER};
		text_format1->SetTrimming(&trimming, NULL);
		//Text_Data text_data = { 100,100,100,100,L"hello",{1.0f,0.0f,0.0f,0.5f},text_format };
		//rect = surface_helper.addRect("rect1", rect_data);
		//text = surface_helper.addText("text1", text_data);

		//surface_helper.compile();

		DC_GUI::Init(dc_inst);
		global_gui = DC_GUI::getInstance();
		s_style1.main_color = { 0.0f,0.0f,0.0f,1.0f };
		s_style1.back_color = { 0.7f,0.7f,1.0f,0.6f };
		s_style1.highlight_color = { 1.0f,0.7f,1.0f,0.7f };
		s_style1.border_color = { 0.1f,0.2f,0.3f,1.0f };
		s_style1.text_format = text_format;
		s_style2 = s_style1;
		s_style2.text_format = text_format1;
		s_style2.back_color = { 1.0f,1.0f,1.0f,0.7f };

		// global_gui->addItem("button1", { 
		// 	DC_GUI_Item::Type::Button,
		// 	DC_GUI_Item::Variant::DirectComposition,
		// 	100,100,100,50,L"Button 1",
		// 	&s_style1
		// });
		// global_gui->addItem("line_edit1", { 
		// 	DC_GUI_Item::Type::LineEdit,
		// 	DC_GUI_Item::Variant::DirectComposition,
		// 	100,200,250,50,L"Line Edit 1",
		// 	& s_style1
		// 	});

        dc_inst.registerEventHandler(WM_MOUSEMOVE, edc::Object::make_closure((void*)mouse_move_handler));
		std::vector<std::wstring> content_list;

		Gerrit_API *api = Gerrit_API::get_instance();
		nlohmann::json changes = api->get_changes("status:open&n=5");
		debug_output(changes.dump(4));
		for (auto &change : changes)
		{
			content_list.push_back(DC_Env::utf8_to_wstring(change["subject"].get<std::string>()));
		}
		std::string patch, id;
		id = changes.front()["id"];
		debug_output(id);
		patch = api->get_patch(id);
		debug_output(patch);
		std::wstring output;

		Ollama_API* ollama_api = Ollama_API::get_instance();

		nlohmann::json messages = nlohmann::json::array({
			{{"role", "system"}, {"content", "请判断下面的patch是否存在潜在问题"}},
			{{"role", "user"}, {"content", patch}}
		});
		debug_output(messages.dump(4));

		nlohmann::json response = ollama_api->chat("qwen2.5:32b-instruct-q8_0", messages);
		debug_output(response.dump(4));
		output = DC_Env::utf8_to_wstring(response["message"]["content"].get<std::string>());


		Easy_Object string_list_view_data_object = global_gui->addItem("string_list_view1", {
			DC_GUI_Item::Type::StringListView,
			DC_GUI_Item::Variant::DirectComposition,
			0,0,500,700,L"",
			content_list,
			& s_style2
			});
		global_gui->addItem("LLM_output",{
			DC_GUI_Item::Type::Text,
			DC_GUI_Item::Variant::DirectComposition,
			500,0,500,700,output,
			{},
			&s_style1
			});
		global_gui->build();

		// Commit changes and run the message loop
		dc_inst.commit();
		ret = dc_inst.Run();
	}
	curl_global_cleanup();
	edc::cleanup();
	return ret;
}

