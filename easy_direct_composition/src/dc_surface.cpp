#include <mutex>
#include <string>
#include <atlbase.h>
#include <dwrite.h>

#include "dc_surface.h"
#include "obj_helper.h"
#include "dc_env.h"
DC_Surface_Helper::DC_Surface_Helper(Easy_Object surface_obj)
    : m_surface_obj(surface_obj) 
{
    auto data = surface_obj.get("data");
    if (!data.is_null())assert(data.has_COM_interface(__uuidof(IDCompositionSurface)));
    else assert(!surface_obj.get("swap_chain").is_null());
    if (surface_obj.get("components").is_null()) {
        surface_obj.insert("components", Easy_Object::make_map());
        Update_Closure_Data update_closure_data;
        update_closure_data.func = static_cast<void (*)(Update_Closure_Data*)>(compile);
        update_closure_data.surface_obj = surface_obj.get_ptr();
        surface_obj.insert("update", Easy_Object::make_raw(&update_closure_data, sizeof(Update_Closure_Data), alignof(Update_Closure_Data)));
        Easy_Object mutex_obj = Easy_Object::make_object<std::mutex>();
        surface_obj.insert("mutex", mutex_obj);
        m_mutex = (std::mutex*)mutex_obj.get_data_ptr();
    }else{
        m_mutex = (std::mutex*)surface_obj.get("mutex").get_data_ptr();
        assert(m_mutex);
    }
}

std::lock_guard<std::mutex> DC_Surface_Helper::lock()
{
    return std::lock_guard<std::mutex>(*m_mutex);
}

static void draw_rect(void* data, ID2D1DeviceContext* render_target, POINT offset)
{
    Rect_Data *rect_data = (Rect_Data*)data;
    ID2D1SolidColorBrush *brush;
    render_target->CreateSolidColorBrush(rect_data->color, &brush);
    render_target->FillRectangle(D2D1::RectF(rect_data->x, rect_data->y, rect_data->x + rect_data->width, rect_data->y + rect_data->height), brush);
}

Easy_Object DC_Surface_Helper::addRect(const std::string &name, Rect_Data rect_data)
{
    Easy_Object rect_obj = Easy_Object::make_map();
    m_surface_obj.get("components").insert(name, rect_obj);
    rect_obj.insert("type", Easy_Object::make_char32_string(U"rect"));
    Easy_Object rect_data_obj = Easy_Object::make_raw(&rect_data, sizeof(Rect_Data), alignof(Rect_Data));
    rect_obj.insert("data", rect_data_obj);
    Component_Draw_Function draw_func = draw_rect;
    rect_obj.insert("draw_func", Easy_Object::make_raw(&draw_func, sizeof(Component_Draw_Function*), alignof(Component_Draw_Function*)));
    return rect_obj;
}

static void draw_line(void* data, ID2D1DeviceContext* render_target, POINT offset)
{
    Line_Data *line_data = (Line_Data*)data;
    ID2D1SolidColorBrush *brush;
    render_target->CreateSolidColorBrush(line_data->color, &brush);
    render_target->DrawLine(
        D2D1::Point2F(line_data->x1, line_data->y1),
        D2D1::Point2F(line_data->x2, line_data->y2),
        brush,
        line_data->stroke_width
    );
}

Easy_Object DC_Surface_Helper::addLine(const std::string &name, Line_Data line_data)
{
    Easy_Object line_obj = Easy_Object::make_map();
    m_surface_obj.get("components").insert(name, line_obj);
    line_obj.insert("type", Easy_Object::make_char32_string(U"line"));
    Easy_Object line_data_obj = Easy_Object::make_raw(&line_data, sizeof(Line_Data), alignof(Line_Data));
    line_obj.insert("data", line_data_obj);
    Component_Draw_Function draw_func = draw_line;
    line_obj.insert("draw_func", Easy_Object::make_raw(&draw_func, sizeof(Component_Draw_Function*), alignof(Component_Draw_Function*)));
    return line_obj;
}

static void draw_rect_border(void* data, ID2D1DeviceContext* render_target, POINT offset)
{
    Line_Data *line_data = (Line_Data*)data;
    ID2D1SolidColorBrush *brush;
    render_target->CreateSolidColorBrush(line_data->color, &brush);
    render_target->DrawRectangle(
        D2D1::RectF(line_data->x1, line_data->y1, line_data->x2, line_data->y2),
        brush,
        line_data->stroke_width
    );
}

Easy_Object DC_Surface_Helper::addRectBorder(const std::string &name, Line_Data line_data)
{
    Easy_Object rect_border_obj = Easy_Object::make_map();
    m_surface_obj.get("components").insert(name, rect_border_obj);
    rect_border_obj.insert("type", Easy_Object::make_char32_string(U"rect_border"));
    Easy_Object rect_border_data_obj = Easy_Object::make_raw(&line_data, sizeof(Line_Data), alignof(Line_Data));
    rect_border_obj.insert("data", rect_border_data_obj);
    Component_Draw_Function draw_func = draw_rect_border;
    rect_border_obj.insert("draw_func", Easy_Object::make_raw(&draw_func, sizeof(Component_Draw_Function*), alignof(Component_Draw_Function*)));
    return rect_border_obj;
}

static void draw_text(void* data, ID2D1DeviceContext* render_target, POINT offset)
{
    Text_Data *text_data = (Text_Data*)data;
    IDWriteTextFormat *text_format = text_data->text_format;
    ID2D1SolidColorBrush *brush;
    UINT32 textLen = (UINT32)wcslen(text_data->text);
    render_target->CreateSolidColorBrush(text_data->color, &brush);
    D2D1_RECT_F layoutRect = D2D1::RectF(text_data->x, text_data->y, text_data->x + text_data->width, text_data->y + text_data->height);
    render_target->DrawTextW(
        text_data->text,
        textLen,
        text_format,
        layoutRect,
        brush,
        D2D1_DRAW_TEXT_OPTIONS_CLIP
    );
}

Easy_Object DC_Surface_Helper::addText(const std::string &name, Text_Data text_data)
{
    Easy_Object text_obj = Easy_Object::make_map();
    m_surface_obj.get("components").insert(name, text_obj);
    text_obj.insert("type", Easy_Object::make_char32_string(U"text"));
    Easy_Object text_data_obj = Easy_Object::make_raw(&text_data, sizeof(Text_Data), alignof(Text_Data));
    text_obj.insert("data", text_data_obj);
    Component_Draw_Function draw_func = draw_text;
    text_obj.insert("draw_func", Easy_Object::make_raw(&draw_func, sizeof(Component_Draw_Function*), alignof(Component_Draw_Function*)));
    return text_obj;
}

void DC_Surface_Helper::clear()
{
    m_surface_obj.get("components").clear();
}

void DC_Surface_Helper::compile()
{
    compile_i(m_surface_obj);
}

void DC_Surface_Helper::compile(Update_Closure_Data* closure)
{
    DC_Surface_Helper::compile_i(closure->surface_obj);
}

void DC_Surface_Helper::compile_i(Easy_Object surface_obj)
{
    Easy_Object surface_data;
    Easy_Object mutex_obj = surface_obj.get("mutex");
    assert(!mutex_obj.is_null());
    std::lock_guard<std::mutex> lock(*((std::mutex*)mutex_obj.get_data_ptr()));
    CComPtr<ID2D1DeviceContext> d2dContext;
    surface_obj.get("context").get_COM_interface(d2dContext);

    // Check if this is a swap chain surface
    Easy_Object swap_chain_obj = surface_obj.get("swap_chain");
    bool isSwapChainSurface = !swap_chain_obj.is_null();

    CComPtr<IDXGISurface> dxgiSurface;
    POINT offset = {0, 0};
    HRESULT hr;

    if (isSwapChainSurface) {
        // For swap chain surfaces, get the back buffer directly
        if (swap_chain_obj.is_null()) return;

        CComPtr<IDXGISwapChain1> swapChain;
        swap_chain_obj.get_COM_interface(swapChain);

        hr = swapChain->GetBuffer(0, IID_PPV_ARGS(&dxgiSurface));
        if (FAILED(hr)) return;
        offset = {0, 0}; // No offset for swap chain surfaces
    } else {
        surface_data = surface_obj.get("data");
        assert(!surface_data.is_null());
        // For regular DirectComposition surfaces
        CComPtr<IDCompositionSurface> surface;
        surface_data.get_COM_interface(surface);

        hr = surface->BeginDraw(NULL, IID_PPV_ARGS(&dxgiSurface), &offset);
        if (FAILED(hr)) return;
    }

    // 1. 获取表面描述
    DXGI_SURFACE_DESC surfaceDesc;
    dxgiSurface->GetDesc(&surfaceDesc);

    // 2. 创建正确的位图属性
    float dpiX, dpiY;
    d2dContext->GetDpi(&dpiX, &dpiY);

    D2D1_BITMAP_PROPERTIES1 bitmapProperties = D2D1::BitmapProperties1(
        D2D1_BITMAP_OPTIONS_TARGET | D2D1_BITMAP_OPTIONS_CANNOT_DRAW,
        D2D1::PixelFormat(
            surfaceDesc.Format,  // 使用表面实际格式
            D2D1_ALPHA_MODE_PREMULTIPLIED),  // 表面格式是DXGI_FORMAT_B8G8R8A8_UNORM
        dpiX,
        dpiY
    );

    // 3. 创建位图
    CComPtr<ID2D1Bitmap1> d2dTargetBitmap;
    hr = d2dContext->CreateBitmapFromDxgiSurface(
        dxgiSurface, 
        &bitmapProperties, 
        &d2dTargetBitmap
    );
    if (FAILED(hr)) return;
    d2dContext->SetTarget(d2dTargetBitmap);

    d2dContext->BeginDraw();
    d2dContext->Clear(D2D1::ColorF(0, 0, 0, 0));
    Easy_Object components = surface_obj.get("components");
    Map_Data *map_data = components.get_map_data();
    for (auto it = map_data->begin(); it != map_data->end(); ) {
        if (Easy_Object(it->second).is_empty())
        {
            it = components.erase(it);
            continue;
        }
        Easy_Object component = it->second;
        Component_Draw_Function draw_func = *((Component_Draw_Function*)component.get("draw_func").get_data_ptr());
        void *data = component.get("data").get_data_ptr();
        draw_func(data, d2dContext, offset);
        ++it;
    }
    d2dContext->EndDraw();

    if (isSwapChainSurface) {
        // For swap chain surfaces, present the frame
        Easy_Object swap_chain_obj = surface_obj.get("swap_chain");
        CComPtr<IDXGISwapChain1> swapChain;
        swap_chain_obj.get_COM_interface(swapChain);
        swapChain->Present(1, 0);
    } else {
        // For regular DirectComposition surfaces, end draw
        CComPtr<IDCompositionSurface> surface;
        surface_data.get_COM_interface(surface);
        surface->EndDraw();
    }
}

bool DC_Surface_Helper::isSwapChainSurface() const
{
    Easy_Object swap_chain_obj = m_surface_obj.get("swap_chain");
    bool isSwapChainSurface = !swap_chain_obj.is_null();
    return isSwapChainSurface;
}

HRESULT DC_Surface_Helper::presentSwapChain()
{
    if (!isSwapChainSurface()) return E_INVALIDARG;

    Easy_Object swap_chain_obj = m_surface_obj.get("swap_chain");
    if (swap_chain_obj.is_null()) return E_INVALIDARG;

    CComPtr<IDXGISwapChain1> swapChain;
    swap_chain_obj.get_COM_interface(swapChain);

    return swapChain->Present(1, 0);
}

HRESULT DC_Surface_Helper::resizeSwapChainBuffers(UINT width, UINT height)
{
    if (!isSwapChainSurface()) return E_INVALIDARG;

    Easy_Object swap_chain_obj = m_surface_obj.get("swap_chain");
    if (swap_chain_obj.is_null()) return E_INVALIDARG;

    CComPtr<IDXGISwapChain1> swapChain;
    swap_chain_obj.get_COM_interface(swapChain);

    // Get current swap chain description
    DXGI_SWAP_CHAIN_DESC1 desc;
    HRESULT hr = swapChain->GetDesc1(&desc);
    if (FAILED(hr)) return hr;

    // Resize buffers
    hr = swapChain->ResizeBuffers(desc.BufferCount, width, height, desc.Format, desc.Flags);
    if (FAILED(hr)) return hr;

    // Update geometry
    Easy_Object geometry_obj = m_surface_obj.get("geometry");
    if (!geometry_obj.is_null()) {
        Surface_Geometry* geometry = (Surface_Geometry*)geometry_obj.get_data_ptr();
        geometry->width = width;
        geometry->height = height;
    }

    return S_OK;
}
