#pragma once

#include <assert.h>
#include <string>
#include <initializer_list>
#include <vector>
#include <dcomp.h>
#include <d2d1.h>
#include <atlbase.h>
#include <atlcomcli.h>
#include "dc_env.h"
#include "dc_surface.h"
#include "dc_env.h"
#include "dc_surface.h"
struct DC_GUI_Style {
    D2D1_COLOR_F border_color;
    D2D1_COLOR_F back_color;
    D2D1_COLOR_F main_color;
    D2D1_COLOR_F highlight_color;
    IDWriteTextFormat* text_format;

};

struct DC_GUI_Item{
    enum class Type {
        Button,
        Text,
        Rect,
        LineEdit,
        StringListView
    } type;
    enum class Variant {
        CommonControl,
        DirectComposition
    } variant;
    float x, y, width, height;
    std::wstring text;
    std::vector<std::wstring> list_items;
    DC_GUI_Style* style;
    int cursor_column;
    float content_start_pos;
    bool dirty_flag;
};


class DC_GUI {
    struct Private {};
public:
    DC_GUI(DC_Env& env, Private);
    static void Init(DC_Env& env);
    static DC_GUI* getInstance();
    ~DC_GUI();

    // Add GUI elements like buttons, text fields, etc.
    Easy_Object addItem(const std::string name, DC_GUI_Item item);
    void addItems(std::initializer_list<std::pair<std::string, DC_GUI_Item>> items);

    void build();

private:
    void buildItem(Easy_Object object, DC_GUI_Item* item_data);
    void buildItemDC(Easy_Object object, DC_GUI_Item* item_data);
    void buildItemCC(Easy_Object object, DC_GUI_Item* item_data);
    DC_Env& m_env;
    Easy_Object m_GUI_root, m_items, m_main_visual, m_main_surface;
    DC_Surface_Helper *m_surface_helper;
};