#include <string>
#include "obj_helper.h"
#include "dc_env.h"
#include "dc_gui.h"
#include <memory>

static std::unique_ptr<DC_GUI> g_dc_gui;

DC_GUI::DC_GUI(DC_Env& env, Private) : m_env(env) {
    m_GUI_root = Easy_Object::make_map();
    m_items = Easy_Object::make_map();
    m_GUI_root.insert("items", m_items);
    m_env.getRootObject().insert("GUI", m_GUI_root);
    m_main_visual = m_env.makeVisual(m_env.getRootVisual());
    m_main_surface = m_env.createSwapChainSurfaceForVisual(m_main_visual, {0, 0, 1000, 700});
    m_surface_helper = new DC_Surface_Helper(m_main_surface);
}

void DC_GUI::Init(DC_Env& env)
{
    Easy_Object::type_register<DC_GUI_Item>(U"DC_GUI_Item");
    g_dc_gui = std::make_unique<DC_GUI>(env, Private{});
}

DC_GUI* DC_GUI::getInstance()
{
    return g_dc_gui.get();
}

DC_GUI::~DC_GUI() {
    delete m_surface_helper;
}

Easy_Object DC_GUI::addItem(const std::string name, DC_GUI_Item item) {
    Easy_Object item_obj = Easy_Object::make_map();
    item.dirty_flag = true;
    Easy_Object item_data = Easy_Object::make_object<DC_GUI_Item>();
    *(DC_GUI_Item*)item_data.get_data_ptr() = item;
    item_obj.insert("data", item_data);
    m_items.insert(name, item_obj);
    return item_obj;
}

void DC_GUI::addItems(std::initializer_list<std::pair<std::string, DC_GUI_Item>> items) {
    for (auto& item : items) {
        addItem(item.first, item.second);
    }
}

void DC_GUI::build() {
    Map_Data* items_data = m_items.get_map_data();
    for (auto& pair : *items_data) {
        Easy_Object item_obj = pair.second;
        DC_GUI_Item* item_data = (DC_GUI_Item*)item_obj.get("data").get_data_ptr();
        if (!item_data->dirty_flag) continue;
        item_data->dirty_flag = false;
        buildItem(pair.second, item_data);
    }
    m_surface_helper->compile();
}

void DC_GUI::buildItem(Easy_Object object, DC_GUI_Item* item_data) {
    if (item_data->variant == DC_GUI_Item::Variant::DirectComposition) {
        buildItemDC(object, item_data);
    } else {
        buildItemCC(object, item_data);
    }
}

void DC_GUI::buildItemDC(Easy_Object object, DC_GUI_Item* item_data) {
    auto style = item_data->style;
    switch (item_data->type) {
        case DC_GUI_Item::Type::Button:
        {            
            auto rect_obj = object.get("rect");
            if (rect_obj.is_null()) {
                rect_obj = m_surface_helper->addRect("rect", Rect_Data{item_data->x, item_data->y, item_data->width, item_data->height, style->back_color});
                object.insert("rect", rect_obj);
            }else{
                auto rect_data = m_surface_helper->getComponentData<Rect_Data>(rect_obj);
                rect_data->x = item_data->x;
                rect_data->y = item_data->y;
                rect_data->width = item_data->width;
                rect_data->height = item_data->height;
                rect_data->color = style->back_color;
            }
            auto border_obj = object.get("border");
            if (border_obj.is_null()) {
                border_obj = m_surface_helper->addRectBorder("border", Line_Data{ item_data->x + 1, item_data->y + 1, item_data->x + item_data->width - 2, item_data->y + item_data->height - 2, style->border_color, 1.0f });
                object.insert("border", border_obj);
            }
            else {
                auto border_data = m_surface_helper->getComponentData<Line_Data>(border_obj);
                border_data->x1 = item_data->x + 1;
                border_data->y1 = item_data->y + 1;
                border_data->x2 = item_data->x + item_data->width - 2;
                border_data->y2 = item_data->y + item_data->height - 2;
                border_data->color = style->border_color;
            }
            auto text_obj = object.get("text");
            if (text_obj.is_null()) {
                text_obj = m_surface_helper->addText("text", Text_Data{item_data->x, item_data->y, item_data->width, item_data->height, item_data->text.c_str(), style->main_color, style->text_format});
                object.insert("text", text_obj);
            }else{
                auto text_data = m_surface_helper->getComponentData<Text_Data>(text_obj);
                text_data->x = item_data->x;
                text_data->y = item_data->y;
                text_data->text = item_data->text.c_str();
                text_data->color = style->main_color;
                text_data->text_format = style->text_format;
            }
            break;
        }        
        case DC_GUI_Item::Type::Text:
        {
            auto rect_obj = object.get("rect");
            if (rect_obj.is_null()) {
                rect_obj = m_surface_helper->addRect("rect", Rect_Data{ item_data->x, item_data->y, item_data->width, item_data->height, style->back_color });
                object.insert("rect", rect_obj);
            }
            else {
                auto rect_data = m_surface_helper->getComponentData<Rect_Data>(rect_obj);
                rect_data->x = item_data->x;
                rect_data->y = item_data->y;
                rect_data->width = item_data->width;
                rect_data->height = item_data->height;
                rect_data->color = style->back_color;
            }
            auto border_obj = object.get("border");
            if (border_obj.is_null()) {
                border_obj = m_surface_helper->addRectBorder("border", Line_Data{ item_data->x + 1, item_data->y + 1, item_data->x + item_data->width - 2, item_data->y + item_data->height - 2, style->border_color, 1.0f });
                object.insert("border", border_obj);
            }
            else {
                auto border_data = m_surface_helper->getComponentData<Line_Data>(border_obj);
                border_data->x1 = item_data->x + 1;
                border_data->y1 = item_data->y + 1;
                border_data->x2 = item_data->x + item_data->width - 2;
                border_data->y2 = item_data->y + item_data->height - 2;
                border_data->color = style->border_color;
            }
            auto text_obj = object.get("text");
            if (text_obj.is_null()) {
                text_obj = m_surface_helper->addText("text", Text_Data{item_data->x, item_data->y, item_data->width, item_data->height, item_data->text.c_str(), style->main_color, style->text_format});
                object.insert("text", text_obj);
            }else{
                auto text_data = m_surface_helper->getComponentData<Text_Data>(text_obj);
                text_data->x = item_data->x;
                text_data->y = item_data->y;
                text_data->text = item_data->text.c_str();
                text_data->color = style->main_color;
                text_data->text_format = style->text_format;
            }
            break;
        }
        case DC_GUI_Item::Type::Rect:
        {
            auto rect_obj = object.get("rect");
            if (rect_obj.is_null()) {
                rect_obj = m_surface_helper->addRect("rect", Rect_Data{item_data->x, item_data->y, item_data->width, item_data->height, style->main_color});
                object.insert("rect", rect_obj);
            }else{
                auto rect_data = m_surface_helper->getComponentData<Rect_Data>(rect_obj);
                rect_data->x = item_data->x;
                rect_data->y = item_data->y;
                rect_data->width = item_data->width;
                rect_data->height = item_data->height;
                rect_data->color = style->main_color;
            }
            break;
        }
        case DC_GUI_Item::Type::LineEdit:
        {
            auto rect_obj = object.get("rect");
            if (rect_obj.is_null()) {
                rect_obj = m_surface_helper->addRect("rect", Rect_Data{ item_data->x, item_data->y, item_data->width, item_data->height, style->back_color });
                object.insert("rect", rect_obj);
            }
            else {
                auto rect_data = m_surface_helper->getComponentData<Rect_Data>(rect_obj);
                rect_data->x = item_data->x;
                rect_data->y = item_data->y;
                rect_data->width = item_data->width;
                rect_data->height = item_data->height;
                rect_data->color = style->back_color;
            }
            auto border_obj = object.get("border");
            if (border_obj.is_null()) {
                border_obj = m_surface_helper->addRectBorder("border", Line_Data{item_data->x + 1, item_data->y + 1, item_data->x + item_data->width - 2, item_data->y + item_data->height - 2, style->border_color, 1.0f});
                object.insert("border", border_obj);
            }else{
                auto border_data = m_surface_helper->getComponentData<Line_Data>(border_obj);
                border_data->x1 = item_data->x + 1;
                border_data->y1 = item_data->y + 1;
                border_data->x2 = item_data->x + item_data->width - 2;
                border_data->y2 = item_data->y + item_data->height - 2;
                border_data->color = style->border_color;
            }
            auto text_obj = object.get("text");
            if (text_obj.is_null()) {
                text_obj = m_surface_helper->addText("text", Text_Data{item_data->x, item_data->y, item_data->width, item_data->height, item_data->text.c_str(), style->main_color, style->text_format});
                object.insert("text", text_obj);
            }else{
                auto text_data = m_surface_helper->getComponentData<Text_Data>(text_obj);
                text_data->x = item_data->x;
                text_data->y = item_data->y;
                text_data->text = item_data->text.c_str();
                text_data->color = style->main_color;
                text_data->text_format = style->text_format;
            }
            auto cursor_obj = object.get("cursor");
            float cursor_x = item_data->x + item_data->cursor_column;
            if (cursor_obj.is_null()) {
                cursor_obj = m_surface_helper->addLine("cursor", Line_Data{cursor_x, item_data->y, cursor_x, item_data->y + item_data->height, style->main_color, 1.0f});
                object.insert("cursor", cursor_obj);
            }else{
                auto cursor_data = m_surface_helper->getComponentData<Line_Data>(cursor_obj);
                cursor_data->x1 = cursor_x;
                cursor_data->y1 = item_data->y;
                cursor_data->x2 = cursor_x;
                cursor_data->y2 = item_data->y + item_data->height;
                cursor_data->color = style->main_color;
            }
            break;
        }
        case DC_GUI_Item::Type::StringListView:
        {
            float item_height = 17;
            int content_start_pos = (int)item_data->content_start_pos;
            auto surface_obj = object.get("surface");
            if (surface_obj.is_null()) {
                auto visual_obj = m_env.makeVisual(m_main_visual);
                surface_obj = m_env.createSwapChainSurfaceForVisual(visual_obj, {item_data->x, item_data->y, (int)item_data->width, (int)item_data->height});
                object.insert("surface", surface_obj);
            }
            DC_Surface_Helper surface_helper(surface_obj);
            surface_helper.clear();
            surface_helper.addRect("rect", Rect_Data{ item_data->x, item_data->y, item_data->width, item_data->height, style->back_color });

            float highlight_y = (item_data->cursor_column - content_start_pos) * item_height;
            surface_helper.addRect("rect_highlight", Rect_Data{ item_data->x + 1, highlight_y + 1, item_data->width - 2, item_height - 2, style->highlight_color });
            if (content_start_pos < 0) content_start_pos = 0;
            else if (content_start_pos > item_data->list_items.size() - 1) break;
            for (size_t i = content_start_pos; i < item_data->list_items.size(); ++i) {
                float y_pos = item_data->y + (i - content_start_pos) * item_height;
                if (y_pos > item_data->y + item_data->height) break;
                auto text_obj = surface_helper.addText("text" + std::to_string(i), Text_Data{item_data->x, y_pos, item_data->width, item_height, item_data->list_items[i].c_str(), style->main_color, style->text_format});
            }
            surface_helper.addRectBorder("border", Line_Data{ item_data->x + 1, item_data->y + 1, item_data->x + item_data->width - 2, item_data->y + item_data->height - 2, style->border_color, 1.0f });
            surface_helper.compile();
            break;
        }
    }

    m_env.markSurfaceDirty(m_main_surface);
}

void DC_GUI::buildItemCC(Easy_Object object, DC_GUI_Item* item_data) {
    switch (item_data->type) {
        case DC_GUI_Item::Type::Button:
        {
            auto window_object = object.get("window");
            if (window_object.is_null()) {
                HWND window = CreateWindowExW(
                    WS_EX_CLIENTEDGE,
                    L"BUTTON",
                    item_data->text.c_str(),
                    WS_CHILD | WS_VISIBLE | BS_PUSHBUTTON,
                    (int)item_data->x,
                    (int)item_data->y,
                    (int)item_data->width,
                    (int)item_data->height,
                    m_env.getMainWindow(),
                    NULL,
                    m_env.getInstance(),
                    NULL
                );
                window_object = Easy_Object::make_HWND_object(window);
                object.insert("window", window_object);
                ShowWindow(window, SW_SHOWDEFAULT);
                //Easy_Object visual = m_env.makeVisual(m_env.getRootVisual());
                //Easy_Object surface = m_env.createChildWindowSurfaceForVisual(visual, {item_data->x, item_data->y, (int)item_data->width, (int)item_data->height}, window, m_env.getMainWindow());
                //object.insert("visual", visual);
                //object.insert("surface", surface);
                HWND warpper_window = m_env.createLayeredWindowWrapper(window, (int)item_data->x, (int)item_data->y, (int)item_data->width, (int)item_data->height);
                auto warpper_object = Easy_Object::make_HWND_object(warpper_window);
                object.insert("warpper", window_object);
            }else{
                MoveWindow(((HWND_Packer*)(window_object.get_data_ptr()))->window, (int)item_data->x, (int)item_data->y, (int)item_data->width, (int)item_data->height, TRUE);
            }
            break;
        }
        case DC_GUI_Item::Type::Text:
        {
            auto window_object = object.get("window");
            if (window_object.is_null()) {
                HWND window = CreateWindowExW(
                    WS_EX_CLIENTEDGE,
                    L"STATIC",
                    item_data->text.c_str(),
                    WS_CHILD | WS_VISIBLE | SS_LEFT,
                    (int)item_data->x,
                    (int)item_data->y,
                    (int)item_data->width,
                    (int)item_data->height,
                    m_env.getMainWindow(),
                    NULL,
                    m_env.getInstance(),
                    NULL
                );
                window_object = Easy_Object::make_HWND_object(window);
                object.insert("window", window_object);
                ShowWindow(window, SW_SHOWDEFAULT);
                //Easy_Object visual = m_env.makeVisual(m_env.getRootVisual());
                //Easy_Object surface = m_env.createChildWindowSurfaceForVisual(visual, {item_data->x, item_data->y, (int)item_data->width, (int)item_data->height}, window, m_env.getMainWindow());
                //object.insert("visual", visual);
                //object.insert("surface", surface);
                HWND warpper_window = m_env.createLayeredWindowWrapper(window, (int)item_data->x, (int)item_data->y, (int)item_data->width, (int)item_data->height);
                auto warpper_object = Easy_Object::make_HWND_object(warpper_window);
                object.insert("warpper", window_object);
            }else{
                MoveWindow(((HWND_Packer*)(window_object.get_data_ptr()))->window, (int)item_data->x, (int)item_data->y, (int)item_data->width, (int)item_data->height, TRUE);
            }
            break;
        }
        case DC_GUI_Item::Type::Rect:
        {
            auto window_object = object.get("window");
            if (window_object.is_null()) {
                HWND window = CreateWindowExW(
                    WS_EX_CLIENTEDGE,
                    L"STATIC",
                    L"",
                    WS_CHILD | WS_VISIBLE | SS_LEFT,
                    (int)item_data->x,
                    (int)item_data->y,
                    (int)item_data->width,
                    (int)item_data->height,
                    m_env.getMainWindow(),
                    NULL,
                    m_env.getInstance(),
                    NULL
                );
                window_object = Easy_Object::make_HWND_object(window);
                object.insert("window", window_object);
                ShowWindow(window, SW_SHOWDEFAULT);
                //Easy_Object visual = m_env.makeVisual(m_env.getRootVisual());
                //Easy_Object surface = m_env.createChildWindowSurfaceForVisual(visual, {item_data->x, item_data->y, (int)item_data->width, (int)item_data->height}, window, m_env.getMainWindow());
                //object.insert("visual", visual);
                //object.insert("surface", surface);
                HWND warpper_window = m_env.createLayeredWindowWrapper(window, (int)item_data->x, (int)item_data->y, (int)item_data->width, (int)item_data->height);
                auto warpper_object = Easy_Object::make_HWND_object(warpper_window);
                object.insert("warpper", window_object);
            }else{
                MoveWindow(((HWND_Packer*)(window_object.get_data_ptr()))->window, (int)item_data->x, (int)item_data->y, (int)item_data->width, (int)item_data->height, TRUE);
            }
            break;
        }
        case DC_GUI_Item::Type::LineEdit:
        {
            auto window_object = object.get("window");
            if (window_object.is_null()) {
                HWND window = CreateWindowExW(
                    WS_EX_CLIENTEDGE,
                    L"EDIT",
                    item_data->text.c_str(),
                    WS_CHILD | WS_VISIBLE | ES_LEFT | WS_BORDER,
                    (int)item_data->x,
                    (int)item_data->y,
                    (int)item_data->width,
                    (int)item_data->height,
                    m_env.getMainWindow(),
                    NULL,
                    m_env.getInstance(),
                    NULL
                );
                window_object = Easy_Object::make_HWND_object(window);
                object.insert("window", window_object);
                ShowWindow(window, SW_SHOWDEFAULT);
                //Easy_Object visual = m_env.makeVisual(m_env.getRootVisual());
                //Easy_Object surface = m_env.createChildWindowSurfaceForVisual(visual, {item_data->x, item_data->y, (int)item_data->width, (int)item_data->height}, window, m_env.getMainWindow());
                //object.insert("visual", visual);
                //object.insert("surface", surface);
                HWND warpper_window = m_env.createLayeredWindowWrapper(window, (int)item_data->x, (int)item_data->y, (int)item_data->width, (int)item_data->height);
                auto warpper_object =  Easy_Object::make_HWND_object(warpper_window);
                object.insert("warpper", window_object);

            }else{
                MoveWindow(((HWND_Packer*)(window_object.get_data_ptr()))->window, (int)item_data->x, (int)item_data->y, (int)item_data->width, (int)item_data->height, TRUE);
            }
            break;
        }
    }
}