# Swap Chain Surfaces

This document describes the swap chain surface functionality added to the Easy Direct Composition library.

## Overview

Swap chain surfaces provide better performance for frequently updated content compared to regular DirectComposition surfaces. They use DXGI swap chains to enable efficient double-buffering and vsync support.

## Key Features

- **Better Performance**: Optimized for frequently updated content
- **Vsync Support**: Smooth animation with proper frame timing
- **Double Buffering**: Eliminates tearing and provides smooth updates
- **Automatic Present**: The compile() method automatically presents frames
- **Buffer Resizing**: Support for dynamic buffer size changes

## API Reference

### DC_Env Methods

#### createSwapChainSurfaceForVisual
```cpp
Easy_Object createSwapChainSurfaceForVisual(Easy_Object visual, Surface_Geometry geometry, UINT buffer_count = 2);
```
Creates a swap chain surface attached to a visual.

**Parameters:**
- `visual`: The visual object to attach the surface to
- `geometry`: Surface dimensions and position
- `buffer_count`: Number of back buffers (default: 2)

**Returns:** Surface object with swap chain support

#### presentSwapChain
```cpp
HRESULT presentSwapChain(Easy_Object swap_chain_surface);
```
Manually presents a swap chain frame.

#### resizeSwapChainBuffers
```cpp
HRESULT resizeSwapChainBuffers(Easy_Object swap_chain_surface, UINT width, UINT height);
```
Resizes the swap chain buffers.

### DC_Surface_Helper Methods

#### isSwapChainSurface
```cpp
bool isSwapChainSurface() const;
```
Checks if the surface is a swap chain surface.

#### presentSwapChain
```cpp
HRESULT presentSwapChain();
```
Presents the current frame for swap chain surfaces.

#### resizeSwapChainBuffers
```cpp
HRESULT resizeSwapChainBuffers(UINT width, UINT height);
```
Resizes the swap chain buffers and updates geometry.

### Convenience Functions

#### createSwapChainSurface
```cpp
edc::Object createSwapChainSurface(edc::Environment& env, edc::Object visual, int width, int height, unsigned int buffer_count = 2);
```
Convenience function to create a swap chain surface.

## Usage Examples

### Basic Usage

```cpp
#include "easy_direct_composition.h"

// Initialize library
edc::initialize();
edc::Environment env(hInstance, edc::Object::get_root());
env.Initialize();

// Create visual and swap chain surface
auto visual = env.makeVisual(env.getRootVisual());
auto surface = edc::createSwapChainSurface(env, visual, 400, 300);

// Create surface helper
edc::SurfaceHelper helper(surface);

// Verify it's a swap chain surface
assert(helper.isSwapChainSurface());

// Add content and render
helper.addRect("background", edc::make_rect(0, 0, 400, 300, edc::make_color(0.2f, 0.3f, 0.8f)));
helper.compile(); // Automatically presents the frame

env.commit();
```

### Animation Loop

```cpp
// Animation variables
float x_pos = 0.0f;
float direction = 1.0f;

// Animation loop
while (running) {
    // Update position
    x_pos += direction * 2.0f;
    if (x_pos >= 300.0f || x_pos <= 0.0f) {
        direction *= -1.0f;
    }
    
    // Update content
    helper.addRect("animated", edc::make_rect(x_pos, 50, 100, 100, edc::make_color(1.0f, 0.5f, 0.0f)));
    helper.compile(); // Presents automatically
    
    // Control frame rate
    std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
}
```

### Handling Window Resize

```cpp
void OnWindowResize(int new_width, int new_height) {
    if (helper.isSwapChainSurface()) {
        HRESULT hr = helper.resizeSwapChainBuffers(new_width, new_height);
        if (SUCCEEDED(hr)) {
            // Re-add content with new dimensions
            helper.addRect("background", edc::make_rect(0, 0, new_width, new_height, background_color));
            helper.compile();
        }
    }
}
```

## Performance Considerations

### When to Use Swap Chain Surfaces

**Use swap chain surfaces for:**
- Frequently updated content (animations, real-time data)
- Game-like applications requiring smooth frame rates
- Content that needs vsync synchronization
- Applications with high frame rate requirements

**Use regular surfaces for:**
- Static or rarely updated content
- UI elements that don't change frequently
- Simple graphics that don't require double buffering

### Performance Tips

1. **Buffer Count**: Use 2 buffers for most applications. More buffers increase memory usage without significant benefit.

2. **Frame Rate Control**: Use appropriate timing mechanisms to control frame rate and avoid unnecessary updates.

3. **Batch Updates**: Group multiple drawing operations before calling compile() to minimize present calls.

4. **Resource Management**: Properly manage surface lifetime and avoid creating unnecessary swap chains.

## Technical Details

### Implementation Notes

- Swap chain surfaces use `DXGI_SWAP_EFFECT_FLIP_SEQUENTIAL` for optimal performance
- Alpha mode is set to `DXGI_ALPHA_MODE_PREMULTIPLIED` for proper composition
- The library automatically handles buffer acquisition and presentation
- Surfaces are created with `DXGI_FORMAT_B8G8R8A8_UNORM` format

### Limitations

- Swap chain surfaces require more memory than regular surfaces
- Not suitable for static content that rarely changes
- Requires DXGI 1.2 or later (Windows 8+)
- May have different behavior on different graphics drivers

## Error Handling

Always check return values when working with swap chain operations:

```cpp
HRESULT hr = helper.resizeSwapChainBuffers(new_width, new_height);
if (FAILED(hr)) {
    // Handle resize failure
    // May need to recreate the swap chain
}

hr = helper.presentSwapChain();
if (FAILED(hr)) {
    // Handle present failure
    // Check for device lost scenarios
}
```

## See Also

- [Basic Example](../examples/basic_example.cpp)
- [Swap Chain Example](../examples/swap_chain_example.cpp)
- [DirectComposition Documentation](https://docs.microsoft.com/en-us/windows/win32/directcomp/directcomposition-portal)
- [DXGI Swap Chains](https://docs.microsoft.com/en-us/windows/win32/direct3ddxgi/dxgi-swap-chains)
