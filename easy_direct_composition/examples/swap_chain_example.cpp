/**
 * @file swap_chain_example.cpp
 * @brief Example demonstrating swap chain surface usage
 * @version 1.0.0
 * 
 * This example shows how to create and use swap chain surfaces with
 * the Easy Direct Composition library. Swap chain surfaces provide
 * better performance for frequently updated content.
 */

#include "../easy_direct_composition.h"
#include <cassert>
#include <chrono>
#include <thread>

/**
 * @brief Application entry point for swap chain example
 * 
 * Creates a window with a swap chain surface that displays animated content.
 * The swap chain allows for smooth animation with proper vsync.
 */
INT WINAPI wWinMain(_In_ HINSTANCE hInstance, _In_opt_ HINSTANCE, _In_ LPWSTR, _In_ INT)
{
    // Initialize the Easy Direct Composition library
    edc::initialize();
    
    // Create the Direct Composition environment
    edc::Environment dc_env(hInstance, edc::Object::get_root());
    
    // Initialize the environment
    HRESULT hr = dc_env.Initialize();
    if (FAILED(hr)) {
        MessageBoxA(nullptr, "Failed to initialize Direct Composition environment", "Error", MB_OK | MB_ICONERROR);
        return -1;
    }
    
    // Create a visual object
    edc::Object visual1 = dc_env.makeVisual(dc_env.getRootVisual());
    
    // Create a swap chain surface instead of regular surface
    // This provides better performance for frequently updated content
    edc::Object swap_chain_surface = edc::createSwapChainSurface(dc_env, visual1, 400, 300, 2);
    
    // Create a surface helper to manage drawing operations
    edc::SurfaceHelper surface_helper(swap_chain_surface);
    
    // Verify this is a swap chain surface
    assert(surface_helper.isSwapChainSurface());
    
    // Add initial content
    edc::RectData background = edc::make_rect(
        0, 0,           // x, y position
        400, 300,       // width, height
        0.1f, 0.1f, 0.2f, 1.0f  // dark blue background
    );
    surface_helper.addRect("background", background);
    
    // Add an animated rectangle
    edc::RectData animated_rect = edc::make_rect(
        50, 50,         // initial position
        100, 100,       // size
        1.0f, 0.5f, 0.0f, 0.8f  // orange color
    );
    surface_helper.addRect("animated", animated_rect);
    
    // Compile and present initial frame
    surface_helper.compile();
    
    // Commit the composition
    dc_env.commit();
    
    // Animation loop variables
    float x_pos = 50.0f;
    float direction = 1.0f;
    const float speed = 2.0f;
    const float max_x = 300.0f;
    const float min_x = 0.0f;
    
    // Simple animation loop
    // In a real application, you would integrate this with your message loop
    for (int frame = 0; frame < 1000; ++frame) {
        // Update animation
        x_pos += direction * speed;
        if (x_pos >= max_x || x_pos <= min_x) {
            direction *= -1.0f;
        }
        
        // Update the animated rectangle position
        animated_rect.x = x_pos;
        
        // Clear and re-add components with updated positions
        // In a more sophisticated implementation, you might want to
        // only update changed components
        surface_helper.addRect("background", background);
        surface_helper.addRect("animated", animated_rect);
        
        // Compile and present the frame
        surface_helper.compile();
        
        // The compile method automatically calls Present() for swap chain surfaces
        // But you can also call it explicitly if needed:
        // surface_helper.presentSwapChain();
        
        // Small delay to control animation speed
        std::this_thread::sleep_for(std::chrono::milliseconds(16)); // ~60 FPS
    }
    
    // Run the main message loop
    return dc_env.Run();
}

/**
 * @brief Example showing swap chain buffer resizing
 * 
 * This function demonstrates how to resize swap chain buffers
 * when the window size changes.
 */
void ResizeExample(edc::Environment& env, edc::Object visual, int new_width, int new_height)
{
    // Get the existing surface
    edc::Object surface = visual.get("surface");
    if (surface.is_null()) return;
    
    edc::SurfaceHelper helper(surface);
    
    // Check if it's a swap chain surface
    if (helper.isSwapChainSurface()) {
        // Resize the swap chain buffers
        HRESULT hr = helper.resizeSwapChainBuffers(new_width, new_height);
        if (SUCCEEDED(hr)) {
            // Re-add content with new dimensions
            edc::RectData new_background = edc::make_rect(
                0, 0, 
                static_cast<float>(new_width), 
                static_cast<float>(new_height),
                0.1f, 0.1f, 0.2f, 1.0f
            );
            helper.addRect("background", new_background);
            helper.compile();
        }
    }
}

/**
 * @brief Performance comparison example
 * 
 * This example shows the performance difference between regular surfaces
 * and swap chain surfaces for frequently updated content.
 */
void PerformanceComparisonExample(HINSTANCE hInstance)
{
    edc::initialize();
    edc::Environment env(hInstance, edc::Object::get_root());
    env.Initialize();
    
    auto visual1 = env.makeVisual(env.getRootVisual());
    auto visual2 = env.makeVisual(env.getRootVisual());
    
    // Move second visual to the right
    env.moveVisual(visual2, 450, 0);
    
    // Create regular surface
    auto regular_surface = env.createSurfaceForVisual(visual1, {0, 0, 400, 300});
    
    // Create swap chain surface
    auto swap_chain_surface = edc::createSwapChainSurface(env, visual2, 400, 300);
    
    edc::SurfaceHelper regular_helper(regular_surface);
    edc::SurfaceHelper swap_chain_helper(swap_chain_surface);
    
    env.commit();
    
    // Performance test: update both surfaces rapidly
    auto start_time = std::chrono::high_resolution_clock::now();
    
    for (int i = 0; i < 100; ++i) {
        float hue = static_cast<float>(i) / 100.0f;
        
        // Update regular surface
        edc::RectData rect1 = edc::make_rect(0, 0, 400, 300, 
            edc::make_color(hue, 0.5f, 1.0f - hue));
        regular_helper.addRect("test", rect1);
        regular_helper.compile();
        
        // Update swap chain surface
        edc::RectData rect2 = edc::make_rect(0, 0, 400, 300, 
            edc::make_color(1.0f - hue, hue, 0.5f));
        swap_chain_helper.addRect("test", rect2);
        swap_chain_helper.compile();
        
        env.commit();
    }
    
    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end_time - start_time);
    
    // In a real application, you would log or display the performance results
    // The swap chain surface should show better performance for frequent updates
}
