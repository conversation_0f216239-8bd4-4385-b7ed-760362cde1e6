#pragma once

#include "../easy_direct_composition/src/obj_helper.h"

#include <nlohmann/json.hpp>
#include <string>
#include <vector>
#include <functional>
#include <curl/curl.h>


// Callback function type for streaming responses
using StreamCallback = std::function<void(const nlohmann::json&)>;

class Ollama_API {
    struct Private{};
public:
    Ollama_API(Easy_Object root_object, Private);
    ~Ollama_API();

    static Ollama_API *get_instance();

    // Generate completion API
    nlohmann::json generate(const std::string& model, const std::string& prompt,
                           bool stream = false, const nlohmann::json& options = {});
    void generate_stream(const std::string& model, const std::string& prompt,
                        StreamCallback callback, const nlohmann::json& options = {});

    // Chat completion API
    nlohmann::json chat(const std::string& model, const nlohmann::json& messages,
                       bool stream = false, const nlohmann::json& options = {});
    void chat_stream(const std::string& model, const nlohmann::json& messages,
                    StreamCallback callback, const nlohmann::json& options = {});

    // Model management APIs
    nlohmann::json list_models();
    nlohmann::json show_model(const std::string& model, bool verbose = false);
    nlohmann::json create_model(const std::string& model, const nlohmann::json& params);
    bool copy_model(const std::string& source, const std::string& destination);
    bool delete_model(const std::string& model);
    nlohmann::json pull_model(const std::string& model, bool stream = false);
    nlohmann::json push_model(const std::string& model, bool stream = false);

    // Embeddings API
    nlohmann::json generate_embeddings(const std::string& model, const std::string& prompt);

    // Running models API
    nlohmann::json list_running_models();

    // Version API
    nlohmann::json get_version();

private:
    void build_common_headers(CURL *curl, const std::string& path);
    nlohmann::json make_request(const std::string& endpoint, const std::string& method = "GET",
                               const nlohmann::json& data = {});
    void make_streaming_request(const std::string& endpoint, const nlohmann::json& data,
                               StreamCallback callback);

    Easy_Object m_root_object;
    struct curl_slist *headers = NULL;
};
