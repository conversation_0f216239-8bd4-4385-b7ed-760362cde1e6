# Ollama API C++ Wrapper

This is a C++ wrapper for the Ollama API that provides easy access to local AI models through the Ollama server.

## Features

The wrapper implements all major Ollama API endpoints:

### Text Generation
- **Generate Completion**: Generate text completions from prompts
- **Chat Completion**: Multi-turn conversations with context
- **Streaming Support**: Real-time streaming responses

### Model Management
- **List Models**: Get all available local models
- **Show Model Info**: Detailed model information and capabilities
- **Create Model**: Create custom models from existing ones
- **Copy Model**: Duplicate models with different names
- **Delete Model**: Remove models from local storage
- **Pull Model**: Download models from Ollama registry
- **Push Model**: Upload models to registry

### Advanced Features
- **Embeddings**: Generate vector embeddings for text
- **Running Models**: List currently loaded models
- **Version Info**: Get Ollama server version

## Configuration

On first use, the API will prompt for configuration:
- **Ollama URL**: Default is `http://localhost:11434`
- **API Key**: Optional, for authenticated servers

Configuration is saved to `%APPDATA%/gerrit_client/ollama_api.json`

## Usage Examples

### Basic Text Generation

```cpp
#include "ollama_api.h"

Ollama_API* api = Ollama_API::get_instance();

// Simple completion
json response = api->generate("llama3.2", "Why is the sky blue?");
std::cout << response["response"].get<std::string>() << std::endl;
```

### Chat Conversation

```cpp
json messages = json::array({
    {{"role", "system"}, {"content", "You are a helpful assistant."}},
    {{"role", "user"}, {"content", "Hello!"}}
});

json response = api->chat("llama3.2", messages);
std::cout << response["message"]["content"].get<std::string>() << std::endl;
```

### Streaming Response

```cpp
StreamCallback callback = [](const json& chunk) {
    if (chunk.contains("response")) {
        std::cout << chunk["response"].get<std::string>() << std::flush;
    }
};

api->generate_stream("llama3.2", "Tell me a story", callback);
```

### Advanced Options

```cpp
json options = {
    {"temperature", 0.8},
    {"top_p", 0.9},
    {"max_tokens", 100},
    {"stop", json::array({".", "!"})}
};

json response = api->generate("llama3.2", "Write a poem", false, options);
```

### Model Management

```cpp
// List all models
json models = api->list_models();

// Get model details
json info = api->show_model("llama3.2", true);

// Create a custom model
json params = {
    {"from", "llama3.2"},
    {"system", "You are a coding assistant."}
};
api->create_model("my-coder", params);

// Copy a model
api->copy_model("llama3.2", "llama3.2-backup");

// Delete a model
api->delete_model("old-model");
```

### Embeddings

```cpp
json response = api->generate_embeddings("llama3.2", "Hello world");
auto embedding = response["embedding"];
std::cout << "Vector size: " << embedding.size() << std::endl;
```

## API Reference

### Core Methods

#### Text Generation
- `json generate(model, prompt, stream=false, options={})` - Generate completion
- `void generate_stream(model, prompt, callback, options={})` - Streaming completion

#### Chat
- `json chat(model, messages, stream=false, options={})` - Chat completion
- `void chat_stream(model, messages, callback, options={})` - Streaming chat

#### Models
- `json list_models()` - List available models
- `json show_model(model, verbose=false)` - Show model information
- `json create_model(model, params)` - Create new model
- `bool copy_model(source, destination)` - Copy model
- `bool delete_model(model)` - Delete model
- `json pull_model(model, stream=false)` - Download model
- `json push_model(model, stream=false)` - Upload model

#### Utilities
- `json generate_embeddings(model, prompt)` - Generate embeddings
- `json list_running_models()` - List loaded models
- `json get_version()` - Get server version

### Options

Common options for generation:
- `temperature` (float): Randomness (0.0-2.0)
- `top_p` (float): Nucleus sampling (0.0-1.0)
- `top_k` (int): Top-k sampling
- `repeat_penalty` (float): Repetition penalty
- `seed` (int): Random seed for reproducibility
- `num_predict` (int): Maximum tokens to generate
- `stop` (array): Stop sequences

### Response Format

#### Generation Response
```json
{
  "model": "llama3.2",
  "created_at": "2023-08-04T19:22:45.499127Z",
  "response": "The sky is blue because...",
  "done": true,
  "total_duration": 5043500667,
  "load_duration": 5025959,
  "prompt_eval_count": 26,
  "prompt_eval_duration": 325953000,
  "eval_count": 290,
  "eval_duration": 4709213000
}
```

#### Chat Response
```json
{
  "model": "llama3.2",
  "created_at": "2023-12-12T14:13:43.416799Z",
  "message": {
    "role": "assistant",
    "content": "Hello! How can I help you today?"
  },
  "done": true
}
```

## Dependencies

- **nlohmann/json**: JSON parsing and generation
- **libcurl**: HTTP client for API requests
- **Easy Direct Composition**: Object system integration

## Building

Include the following files in your project:
- `ollama_api.h`
- `ollama_api.cpp`
- Link with libcurl and nlohmann_json

## Error Handling

The API returns empty JSON objects on errors. Always check if responses are empty:

```cpp
json response = api->generate("model", "prompt");
if (response.empty()) {
    std::cerr << "API call failed" << std::endl;
}
```

## Thread Safety

The API is not thread-safe. Use separate instances or add synchronization for multi-threaded usage.
