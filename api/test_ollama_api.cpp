/**
 * @file test_ollama_api.cpp
 * @brief Simple test to verify Ollama API compilation and basic functionality
 */

#include "ollama_api.h"
#include <iostream>
#include <nlohmann/json.hpp>

using json = nlohmann::json;

int main() {
    std::cout << "Testing Ollama API Wrapper..." << std::endl;
    
    try {
        // Test API instance creation
        Ollama_API* api = Ollama_API::get_instance();
        if (api) {
            std::cout << "✓ API instance created successfully" << std::endl;
        } else {
            std::cout << "✗ Failed to create API instance" << std::endl;
            return 1;
        }
        
        // Test version endpoint (should work even if no models are available)
        std::cout << "Testing version endpoint..." << std::endl;
        json version = api->get_version();
        if (!version.empty()) {
            std::cout << "✓ Version endpoint working" << std::endl;
            if (version.contains("version")) {
                std::cout << "  Ollama version: " << version["version"].get<std::string>() << std::endl;
            }
        } else {
            std::cout << "⚠ Version endpoint returned empty (server may not be running)" << std::endl;
        }
        
        // Test list models endpoint
        std::cout << "Testing list models endpoint..." << std::endl;
        json models = api->list_models();
        if (!models.empty()) {
            std::cout << "✓ List models endpoint working" << std::endl;
            if (models.contains("models")) {
                auto model_list = models["models"];
                std::cout << "  Found " << model_list.size() << " models" << std::endl;
                
                // Show first few models
                for (size_t i = 0; i < std::min(3UL, model_list.size()); ++i) {
                    if (model_list[i].contains("name")) {
                        std::cout << "  - " << model_list[i]["name"].get<std::string>() << std::endl;
                    }
                }
            }
        } else {
            std::cout << "⚠ List models returned empty (no models or server not running)" << std::endl;
        }
        
        // Test running models endpoint
        std::cout << "Testing running models endpoint..." << std::endl;
        json running = api->list_running_models();
        if (!running.empty()) {
            std::cout << "✓ Running models endpoint working" << std::endl;
            if (running.contains("models")) {
                auto running_list = running["models"];
                std::cout << "  " << running_list.size() << " models currently loaded" << std::endl;
            }
        } else {
            std::cout << "⚠ Running models returned empty" << std::endl;
        }
        
        std::cout << std::endl << "Basic API test completed!" << std::endl;
        std::cout << "Note: Some endpoints may return empty if Ollama server is not running" << std::endl;
        std::cout << "      or no models are installed." << std::endl;
        
    } catch (const std::exception& e) {
        std::cerr << "✗ Error during testing: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
