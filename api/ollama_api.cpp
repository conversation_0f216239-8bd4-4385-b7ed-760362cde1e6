#include "ollama_api.h"
#include <memory>
#include <fstream>
#include <nlohmann/json.hpp>
#include <string>
#include <vector>
#include <codecvt>
#include <sstream>
#include <windows.h>
#include <curl/curl.h>
#include "../easy_direct_composition/src/dc_env.h"

using json = nlohmann::json;

static std::unique_ptr<Ollama_API> g_instance;

struct Ollama_API_Config {
    std::string url = "http://localhost:11434";
    std::string api_key = "";
};

namespace nlohmann {
    template <>
    struct adl_serializer<Ollama_API_Config> {
        static void to_json(json& j, const Ollama_API_Config& c) {
            j = json{{"url", c.url}, {"api_key", c.api_key}};
        }
        static void from_json(const json& j, Ollama_API_Config& c) {
            if (j.contains("url")) c.url = j["url"].get<std::string>();
            if (j.contains("api_key")) c.api_key = j["api_key"].get<std::string>();
        }
    };
}

static std::unique_ptr<Ollama_API_Config> g_config;


static void load_config()
{
    if (g_config) return;
    std::wstring path = DC_Env::getConfigPath(L"gerrit_client", L"ollama_api.json");
    std::ifstream f(path);
    if (!f.is_open())
    {
        g_config = std::make_unique<Ollama_API_Config>();
        DC_Env *env = DC_Env::get_application();
        if (env) {
            std::wstring url_input = env->execDialog({L"Input Ollama URL", L"Please input the Ollama URL", L"http://localhost:11434"});
            if (!url_input.empty()) {
                g_config->url = DC_Env::wstring_to_utf8(url_input);
            }
            std::wstring api_key_input = env->execDialog({L"Input Ollama API Key", L"Please input the Ollama API Key"});
            if (!api_key_input.empty()) {
                g_config->api_key = DC_Env::wstring_to_utf8(api_key_input);
            }
        }
        std::ofstream f(path);
        f << json(*g_config);
        return;
    }
    json j;
    f >> j;
    g_config = std::make_unique<Ollama_API_Config>(j.get<Ollama_API_Config>());
}

static size_t WriteCallback(void *contents, size_t size, size_t nmemb, void *userp) {
    ((std::string*)userp)->append((char*)contents, size * nmemb);
    return size * nmemb;
}

static size_t StreamCallbackFunc(void *contents, size_t size, size_t nmemb, void *userp) {
    StreamCallback* callback = static_cast<StreamCallback*>(userp);
    std::string chunk((char*)contents, size * nmemb);

    // Parse each line as JSON (streaming format)
    std::istringstream stream(chunk);
    std::string line;
    while (std::getline(stream, line)) {
        if (!line.empty() && line != "\n") {
            try {
                json j = json::parse(line);
                (*callback)(j);
            } catch (const std::exception&) {
                // Ignore malformed JSON lines
            }
        }
    }
    return size * nmemb;
}

Ollama_API::Ollama_API(Easy_Object root_object, Private)
    : m_root_object(root_object)
{
    load_config();

    headers = curl_slist_append(headers, "Accept: application/json");
    headers = curl_slist_append(headers, "Content-Type: application/json");
    if (!g_config->api_key.empty()) {
        std::string auth_header = "Authorization: Bearer " + g_config->api_key;
        headers = curl_slist_append(headers, auth_header.c_str());
    }
}

Ollama_API::~Ollama_API()
{
    curl_slist_free_all(headers);
}

Ollama_API *Ollama_API::get_instance()
{
    if (!g_instance) {
        g_instance = std::make_unique<Ollama_API>(Easy_Object::get_root(), Private());
    }
    return g_instance.get();
}

void Ollama_API::build_common_headers(CURL *curl, const std::string& path)
{
    std::string full_url = g_config->url + path;
    curl_easy_setopt(curl, CURLOPT_URL, full_url.c_str());
    curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);
}

json Ollama_API::make_request(const std::string& endpoint, const std::string& method, const json& data)
{
    std::string readBuffer;
    CURL *curl = curl_easy_init();
    std::string json_str;

    if (!curl) return json();

    build_common_headers(curl, endpoint);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &readBuffer);

    if (!data.empty())
    {
        json_str = data.dump();
        if (method == "POST") {
            curl_easy_setopt(curl, CURLOPT_POST, 1L);
            curl_easy_setopt(curl, CURLOPT_POSTFIELDS, json_str.c_str());
        }
        else if (method == "DELETE") {
            curl_easy_setopt(curl, CURLOPT_CUSTOMREQUEST, "DELETE");
            curl_easy_setopt(curl, CURLOPT_POSTFIELDS, json_str.c_str());
        }
    }

    CURLcode res = curl_easy_perform(curl);
    curl_easy_cleanup(curl);

    if (res != CURLE_OK || readBuffer.empty()) {
        return json();
    }

    try {
        return json::parse(readBuffer);
    } catch (const std::exception&) {
        return json();
    }
}

void Ollama_API::make_streaming_request(const std::string& endpoint, const json& data, StreamCallback callback)
{
    CURL *curl = curl_easy_init();
    if (!curl) return;

    build_common_headers(curl, endpoint);
    curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, StreamCallbackFunc);
    curl_easy_setopt(curl, CURLOPT_WRITEDATA, &callback);
    curl_easy_setopt(curl, CURLOPT_POST, 1L);

    std::string json_str = data.dump();
    curl_easy_setopt(curl, CURLOPT_POSTFIELDS, json_str.c_str());

    curl_easy_perform(curl);
    curl_easy_cleanup(curl);
}

// Generate completion API
json Ollama_API::generate(const std::string& model, const std::string& prompt, bool stream, const json& options)
{
    json request_data = {
        {"model", model},
        {"prompt", prompt},
        {"stream", stream}
    };

    if (!options.empty()) {
        for (auto& [key, value] : options.items()) {
            request_data[key] = value;
        }
    }

    return make_request("/api/generate", "POST", request_data);
}

void Ollama_API::generate_stream(const std::string& model, const std::string& prompt, StreamCallback callback, const json& options)
{
    json request_data = {
        {"model", model},
        {"prompt", prompt},
        {"stream", true}
    };

    if (!options.empty()) {
        for (auto& [key, value] : options.items()) {
            request_data[key] = value;
        }
    }

    make_streaming_request("/api/generate", request_data, callback);
}

// Chat completion API
json Ollama_API::chat(const std::string& model, const json& messages, bool stream, const json& options)
{
    json request_data = {
        {"model", model},
        {"messages", messages},
        {"stream", stream}
    };

    if (!options.empty()) {
        for (auto& [key, value] : options.items()) {
            request_data[key] = value;
        }
    }

    return make_request("/api/chat", "POST", request_data);
}

void Ollama_API::chat_stream(const std::string& model, const json& messages, StreamCallback callback, const json& options)
{
    json request_data = {
        {"model", model},
        {"messages", messages},
        {"stream", true}
    };

    if (!options.empty()) {
        for (auto& [key, value] : options.items()) {
            request_data[key] = value;
        }
    }

    make_streaming_request("/api/chat", request_data, callback);
}

// Model management APIs
json Ollama_API::list_models()
{
    return make_request("/api/tags", "GET");
}

json Ollama_API::show_model(const std::string& model, bool verbose)
{
    json request_data = {
        {"model", model},
        {"verbose", verbose}
    };

    return make_request("/api/show", "POST", request_data);
}

json Ollama_API::create_model(const std::string& model, const json& params)
{
    json request_data = {
        {"model", model}
    };

    for (auto& [key, value] : params.items()) {
        request_data[key] = value;
    }

    return make_request("/api/create", "POST", request_data);
}

bool Ollama_API::copy_model(const std::string& source, const std::string& destination)
{
    json request_data = {
        {"source", source},
        {"destination", destination}
    };

    json response = make_request("/api/copy", "POST", request_data);
    return !response.empty();
}

bool Ollama_API::delete_model(const std::string& model)
{
    json request_data = {
        {"model", model}
    };

    json response = make_request("/api/delete", "DELETE", request_data);
    return !response.empty();
}

json Ollama_API::pull_model(const std::string& model, bool stream)
{
    json request_data = {
        {"model", model},
        {"stream", stream}
    };

    return make_request("/api/pull", "POST", request_data);
}

json Ollama_API::push_model(const std::string& model, bool stream)
{
    json request_data = {
        {"model", model},
        {"stream", stream}
    };

    return make_request("/api/push", "POST", request_data);
}

// Embeddings API
json Ollama_API::generate_embeddings(const std::string& model, const std::string& prompt)
{
    json request_data = {
        {"model", model},
        {"prompt", prompt}
    };

    return make_request("/api/embeddings", "POST", request_data);
}

// Running models API
json Ollama_API::list_running_models()
{
    return make_request("/api/ps", "GET");
}

// Version API
json Ollama_API::get_version()
{
    return make_request("/api/version", "GET");
}

