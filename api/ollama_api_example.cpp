/**
 * @file ollama_api_example.cpp
 * @brief Example usage of the Ollama API wrapper
 * 
 * This file demonstrates how to use the Ollama API wrapper to interact
 * with a local Ollama server for various AI model operations.
 */

#include "ollama_api.h"
#include <iostream>
#include <nlohmann/json.hpp>

using json = nlohmann::json;

void example_list_models() {
    std::cout << "=== Listing Available Models ===" << std::endl;
    
    Ollama_API* api = Ollama_API::get_instance();
    json models = api->list_models();
    
    if (!models.empty() && models.contains("models")) {
        for (const auto& model : models["models"]) {
            std::cout << "Model: " << model["name"].get<std::string>() << std::endl;
            std::cout << "  Size: " << model["size"].get<size_t>() << " bytes" << std::endl;
            if (model.contains("details")) {
                auto details = model["details"];
                if (details.contains("parameter_size")) {
                    std::cout << "  Parameters: " << details["parameter_size"].get<std::string>() << std::endl;
                }
            }
            std::cout << std::endl;
        }
    } else {
        std::cout << "No models found or error occurred." << std::endl;
    }
}

void example_generate_completion() {
    std::cout << "=== Generate Completion ===" << std::endl;
    
    Ollama_API* api = Ollama_API::get_instance();
    
    // Simple completion
    json response = api->generate("llama3.2", "Why is the sky blue?", false);
    
    if (!response.empty() && response.contains("response")) {
        std::cout << "Response: " << response["response"].get<std::string>() << std::endl;
        
        if (response.contains("total_duration")) {
            std::cout << "Total duration: " << response["total_duration"].get<long long>() << " ns" << std::endl;
        }
    } else {
        std::cout << "Error: No response received." << std::endl;
    }
}

void example_generate_completion_with_options() {
    std::cout << "=== Generate Completion with Options ===" << std::endl;
    
    Ollama_API* api = Ollama_API::get_instance();
    
    // Completion with custom options
    json options = {
        {"temperature", 0.8},
        {"top_p", 0.9},
        {"max_tokens", 100}
    };
    
    json response = api->generate("llama3.2", "Write a short poem about programming.", false, options);
    
    if (!response.empty() && response.contains("response")) {
        std::cout << "Poem: " << response["response"].get<std::string>() << std::endl;
    } else {
        std::cout << "Error: No response received." << std::endl;
    }
}

void example_chat_completion() {
    std::cout << "=== Chat Completion ===" << std::endl;
    
    Ollama_API* api = Ollama_API::get_instance();
    
    // Create a conversation
    json messages = json::array({
        {
            {"role", "system"},
            {"content", "You are a helpful assistant that explains complex topics simply."}
        },
        {
            {"role", "user"},
            {"content", "Explain quantum computing in simple terms."}
        }
    });
    
    json response = api->chat("llama3.2", messages, false);
    
    if (!response.empty() && response.contains("message")) {
        auto message = response["message"];
        if (message.contains("content")) {
            std::cout << "Assistant: " << message["content"].get<std::string>() << std::endl;
        }
    } else {
        std::cout << "Error: No response received." << std::endl;
    }
}

void example_streaming_completion() {
    std::cout << "=== Streaming Completion ===" << std::endl;
    
    Ollama_API* api = Ollama_API::get_instance();
    
    std::cout << "Streaming response: ";
    
    // Define a callback for streaming
    StreamCallback callback = [](const json& chunk) {
        if (chunk.contains("response") && !chunk["response"].get<std::string>().empty()) {
            std::cout << chunk["response"].get<std::string>() << std::flush;
        }
        
        if (chunk.contains("done") && chunk["done"].get<bool>()) {
            std::cout << std::endl << "Stream completed." << std::endl;
        }
    };
    
    api->generate_stream("llama3.2", "Tell me a short story about a robot.", callback);
}

void example_model_info() {
    std::cout << "=== Model Information ===" << std::endl;
    
    Ollama_API* api = Ollama_API::get_instance();
    
    json info = api->show_model("llama3.2", true);
    
    if (!info.empty()) {
        if (info.contains("details")) {
            auto details = info["details"];
            std::cout << "Model Details:" << std::endl;
            if (details.contains("family")) {
                std::cout << "  Family: " << details["family"].get<std::string>() << std::endl;
            }
            if (details.contains("parameter_size")) {
                std::cout << "  Parameter Size: " << details["parameter_size"].get<std::string>() << std::endl;
            }
            if (details.contains("quantization_level")) {
                std::cout << "  Quantization: " << details["quantization_level"].get<std::string>() << std::endl;
            }
        }
        
        if (info.contains("template")) {
            std::cout << "  Template: " << info["template"].get<std::string>().substr(0, 100) << "..." << std::endl;
        }
    } else {
        std::cout << "Error: Could not retrieve model information." << std::endl;
    }
}

void example_embeddings() {
    std::cout << "=== Generate Embeddings ===" << std::endl;
    
    Ollama_API* api = Ollama_API::get_instance();
    
    json response = api->generate_embeddings("llama3.2", "Hello, world!");
    
    if (!response.empty() && response.contains("embedding")) {
        auto embedding = response["embedding"];
        std::cout << "Embedding vector size: " << embedding.size() << std::endl;
        std::cout << "First 5 values: ";
        for (size_t i = 0; i < std::min(5UL, embedding.size()); ++i) {
            std::cout << embedding[i].get<double>() << " ";
        }
        std::cout << std::endl;
    } else {
        std::cout << "Error: Could not generate embeddings." << std::endl;
    }
}

int main() {
    std::cout << "Ollama API Example" << std::endl;
    std::cout << "==================" << std::endl << std::endl;
    
    try {
        // Initialize the API (this will prompt for configuration if needed)
        Ollama_API* api = Ollama_API::get_instance();
        
        // Run examples
        example_list_models();
        std::cout << std::endl;
        
        example_generate_completion();
        std::cout << std::endl;
        
        example_generate_completion_with_options();
        std::cout << std::endl;
        
        example_chat_completion();
        std::cout << std::endl;
        
        example_streaming_completion();
        std::cout << std::endl;
        
        example_model_info();
        std::cout << std::endl;
        
        example_embeddings();
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        return 1;
    }
    
    return 0;
}
